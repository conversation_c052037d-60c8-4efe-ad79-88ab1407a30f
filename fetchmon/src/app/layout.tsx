import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { ServerStatusProvider } from '@/context/ServerStatusContext';
import { SystemStatusProvider } from '@/context/SystemStatusContext';

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Binance Tick Collector Dashboard",
  description: "Real-time monitoring dashboard for Binance Futures tick data collector",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <ServerStatusProvider>
          <SystemStatusProvider>
            {children}
          </SystemStatusProvider>
        </ServerStatusProvider>
      </body>
    </html>
  );
}
