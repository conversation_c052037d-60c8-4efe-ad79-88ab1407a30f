import { NextRequest, NextResponse } from 'next/server';
// import { getTradingPairData } from '@root/fetchers/db-query-utils';

interface TradingPairData {
  lastRecordTime: number;
  totalRecords: number;
  closedKlineCount: number;
  lastTimestamp: string;
}

interface ApiResponse {
  [symbol: string]: TradingPairData;
}

interface TradingPair {
  symbol: string;
  status: 'active' | 'inactive' | 'error';
  lastUpdate: number;
  errorCount: number;
  recordCount: number;
  lastRecordTime: number | null;
  totalRecords: number;
  closedKlineCount: number;
  lastTimestamp?: string;
  error?: string;
  id: number | null;
  isConnected?: boolean;
}

interface TickCollectorItemType {
  id: number;
  url: string;
  isConnected: boolean;
  lastDataReceived: number;
  timeSinceLastData: number;
  reconnectAttempts: number;
  maxReconnectAttempts: number;
}


export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const symbolsParam = searchParams.get('symbols');
    const dtBop = Date.now();
    const configResponse = await fetch('http://localhost:3001/api/server/config');
    const configData = await configResponse.json();

    const symbols: string[] = configData?.config?.pairs || [];
    const statusResponse = await fetch('http://localhost:3000/status');
    const statusData = await statusResponse.json();
    const tickCollector = statusData?.tickerStatus?.tickCollector || {};

    // Fetch data for all symbols in parallel
    const results = await Promise.allSettled<TradingPair>(
      symbols.map(async (symbol: string): Promise<TradingPair> => {
        const response = await fetch('http://localhost:3000/pairs');
        const dbData: ApiResponse = await response.json();

        if (!dbData[symbol.toUpperCase()]) {
          return {
            symbol: symbol.toUpperCase(),
            status: 'inactive' as const,
            lastUpdate: Date.now(),
            errorCount: 1,
            recordCount: 0,
            lastRecordTime: null,
            totalRecords: 0,
            closedKlineCount: 0,
            error: `Symbol ${symbol.toUpperCase()} not found`,
            id: null,
            isConnected: false,
          };
        }

        const symbolData = dbData[symbol.toUpperCase()];

        const pairData = {
          symbol: symbol.toUpperCase(),
          lastRecordTime: symbolData.lastRecordTime,
          totalRecords: symbolData.totalRecords,
          closedKlineCount: symbolData.closedKlineCount,
          lastTimestamp: symbolData.lastTimestamp
        };

        return {
          symbol: symbol.toUpperCase(),
          status: 'active' as const,
          lastUpdate: Date.now(),
          errorCount: 0,
          recordCount: symbolData.totalRecords,
          lastRecordTime: symbolData.lastRecordTime,
          totalRecords: symbolData.totalRecords,
          closedKlineCount: symbolData.closedKlineCount,
          lastTimestamp: symbolData.lastTimestamp,
          id: null,
          isConnected: false,
        };
      })
    );

    // Process results
    const tradingPairs: TradingPair[] = results.map((result: PromiseSettledResult<TradingPair>, index: number) => {
      const symbol = symbols[index];
      let isConnected = false;
      let id: number | null = null;
      for (const key in tickCollector) {
        if (tickCollector.hasOwnProperty(key)) {
          const item = tickCollector[key] as TickCollectorItemType;
          if (item.url.includes(symbol.toLowerCase())) {
            isConnected = item.isConnected;
            id = item.id;
            // console.log('Found connection for', symbol, 'id:', id, 'isConnected:', isConnected, tickCollector);
            break;
          }
        }
      }

      if (result.status === 'fulfilled') {
        return {
          ...result.value,
          isConnected,
          id,
        };
      } else {
        return {
          symbol: symbols[index],
          status: 'error' as const,
          lastUpdate: Date.now(),
          errorCount: 1,
          recordCount: 0,
          lastRecordTime: null,
          totalRecords: 0,
          closedKlineCount: 0,
          error: result.reason?.message || 'Failed to fetch data',
          isConnected,
          id,
        };
    }
  });

    return NextResponse.json({
      success: true,
      count: tradingPairs.length,
      tradingPairs,
      elapsed: Date.now() - dtBop,
    });

  } catch (error) {
    console.error('Error fetching trading pairs data:', error);
    
    return NextResponse.json(
      {
        error: 'Failed to fetch trading pairs data',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
