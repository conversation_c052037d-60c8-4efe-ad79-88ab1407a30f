// fetchmon/src/app/api/server/ping/route.js

import { NextResponse } from 'next/server';

import fs from 'fs/promises'; // Use fs.promises for async file operations
import path from 'path';

const logFilePath = path.join(process.cwd(), 'restart.log'); // Log file in the project root

async function logToFile(message) {
  try {
    await fs.appendFile(logFilePath, `${new Date().toISOString()} - ${message}\n`);
  } catch (error) {
    console.error('Error writing to log file:', error);
  }
}

export async function GET(request) {
  try {
    const pingResponse = await fetch('http://localhost:3000/status');

    if (!pingResponse.ok) {
      logToFile(`Ping failed with status: ${pingResponse.status}`);
      // If the ping fails, return a 500 status
      return NextResponse.json({ error: 'System Ping failed' }, {
        status: 500,
        statusText: 'Ping failed'
      });
    }

    const data = await pingResponse.json();
    return NextResponse.json(data); // Forward the response from the ping

  } catch (error) {
    logToFile(`Ping error: ${error.message}`);
    logToFile('Server not reachable, returning 500 status');
    // console.error('Ping error:', error);
    return NextResponse.json({ error: 'Ping failed, Server not reachable' },
      {
        status: 500,
        statusText: 'Server not reachable'
      });
  }
}