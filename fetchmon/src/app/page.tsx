/* eslint-disable @typescript-eslint/no-unused-expressions */
// Main dashboard page
'use client';

import { useSystemStatus } from '@/hooks/useSystemStatus';
// import { SystemOverview } from '@/components/dashboard/system-overview';
// import { PerformanceCharts } from '@/components/dashboard/performance-charts';
import { TradingPairs } from '@/components/dashboard/trading-pairs';
import { ProblemPairs } from '@/components/dashboard/problem-pairs';
import { SystemDetails } from '@/components/dashboard/system-details';
// import { ActivityLog } from '@/components/dashboard/activity-log';
import SystemConfig from '@/components/dashboard/system-confix';
import { Button } from '@/components/ui/button';
import { RefreshCw, Server,  CheckCircle,  AlertTriangle } from 'lucide-react';
import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { SystemStatusCard } from '@/components/dashboard/system-status-card';

import { useServerStatus } from '@/context/ServerStatusContext';

import { useSystemHealthStatus } from '@/context/SystemStatusContext';

export default function Dashboard() {
  const { serverStatus, serverStatusLoading, loadingMessage, pingServer, startStopServer } = useServerStatus();
  
  const { health, status, metrics, loading, error, refresh } = useSystemStatus();
  const [actionLoading, setActionLoading] = useState(false);
  
  // const { 
  //   systemStatus: systemHealthStatus, 
  //   systemStatusLoading: systemHealthLoading, 
  //   loadingMessage: systemLoadingMessage, 
  //   pingSystem, 
  //   startStopSystem 
  // } = useSystemHealthStatus();


  const handleStartPair = async (pair: string) => {
    setActionLoading(true);
    try {
      // In a real implementation, this would call the API
      console.log(`Starting pair: ${pair}`);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      // Refresh data after action
      refresh();
    } catch (err) {
      console.error(`Error starting pair ${pair}:`, err);
    } finally {
      setActionLoading(false);
    }
  };
  
  const handleStopPair = async (pair: string) => {
    setActionLoading(true);
    try {
      // In a real implementation, this would call the API
      console.log(`Stopping pair: ${pair}`);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      // Refresh data after action
      refresh();
    } catch (err) {
      console.error(`Error stopping pair ${pair}:`, err);
    } finally {
      setActionLoading(false);
    }
  };
  
  const handleRestartPair = async (pair: string) => {
    setActionLoading(true);
    try {
      // In a real implementation, this would call the API
      console.log(`Restarting pair: ${pair}`);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      // Refresh data after action
      refresh();
    } catch (err) {
      console.error(`Error restarting pair ${pair}:`, err);
    } finally {
      setActionLoading(false);
    }
  };

  const startStopServerFn = async (type: 'start' | 'stop') => {
    // await startStopSystem(type);
    // await new Promise(resolve => setTimeout(resolve, 800));
    await startStopServer(type);
  };
  
  const bgColor = serverStatus === 'healthy' ? 'bg-yellow-200' : null;

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="container max-w-7xl py-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-top md:justify-between gap-4 mb-8">
          <div>
            <h1 className="text-3xl font-bold">Binance Tick Collector</h1>
            <p className="text-muted-foreground">
              Real-time monitoring dashboard
            </p>
          </div>
          
          <div className="flex items-center gap-2">
            <SystemStatusCard />
            {/* Server Status Card */}
            <Card className={`${bgColor} w-70 py-4 gap-2 min-h-[120px]`}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Server Status &nbsp;&nbsp;<span className='text-xs text-muted-foreground cursor-pointer' onClick={pingServer}>check</span></CardTitle>
                {serverStatus === 'healthy' ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <AlertTriangle className="h-4 w-4 text-red-500" />
                )}
              </CardHeader>

              <CardContent className='flex justify-between'>
                <div>
                  <div className="text-2xl font-bold">
                    {serverStatusLoading ? (
                      <div className="h-6 w-20 bg-gray-200 rounded animate-pulse" />
                    ) : serverStatus === 'healthy' ? (
                      'Running'
                    ) : (
                      'Stopped'
                    )}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {serverStatusLoading ? (
                      <div className="h-3 w-24 bg-gray-200 rounded animate-pulse mt-1" />
                    ) : serverStatus === 'healthy' ? (
                      'All systems operational'
                    ) : (
                      'Server is not active'
                    )}
                  </div>
                </div>
                <div className='flex justify-end'>
                  <span className='text-xs text-muted-foreground text-align-right'>
                    {serverStatusLoading ? (
                      loadingMessage
                    ) : serverStatus === 'healthy' ? (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={async () => {
                          if (window.confirm('Are you sure you want to stop the server?')) {
                            await startStopServerFn('stop');
                          }
                        }}
                      >
                        Stop
                      </Button>
                    ) : (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={async () => {
                          if (window.confirm('Are you sure you want to start the server?')) {
                            await startStopServer('start');
                          }
                        }}
                      >
                        Start
                      </Button>
                    )}


                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
        
        {/* Error Banner */}
        {serverStatus !== 'healthy' && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center text-red-800">
              <span className="font-medium">Warning:</span>
              <span className="ml-2">{'server is not active'}</span>
            </div>
          </div>
        )}

        {/* System Config Cards */}
        <div className="mb-8">
          <SystemConfig />
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Left Column - Trading Pairs */}
          <div className="lg:col-span-2">
            <TradingPairs 
              status={status} 
              loading={loading} 
              onStartPair={handleStartPair}
              onStopPair={handleStopPair}
            />
          </div>
          
        </div>
        
        {/* Main Content Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
          {/* Right Column - Problem Pairs and System Details */}
          <ProblemPairs 
              status={status} 
              loading={loading} 
              onStartPair={handleStartPair}
              onRestartPair={handleRestartPair}
            />
            
            <SystemDetails 
              status={status} 
              metrics={metrics} 
              loading={loading} 
            />
        </div>
        {/* Activity Log */}
        {/* <div className="mb-8">
          <ActivityLog loading={loading} />
        </div> */}
      </div>
    </div>
  );
}
