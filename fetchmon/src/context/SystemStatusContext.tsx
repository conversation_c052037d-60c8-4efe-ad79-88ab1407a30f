"use client";
import React, { createContext, useState, useEffect, useContext, ReactNode } from 'react';

type SystemStatusType = 'healthy' | 'unhealthy' | 'error';

interface SystemStatusContextType {
  systemStatus: SystemStatusType;
  systemStatusLoading: boolean;
  loadingMessage: string;
  pingSystem: () => Promise<void>;
  startStopSystem: (type: 'start' | 'stop') => Promise<void>;
}

const SystemStatusContext = createContext<SystemStatusContextType | undefined>(undefined);

export const SystemStatusProvider = ({ children }: { children: ReactNode }) => {
  const [systemStatusLoading, setSystemStatusLoading] = useState(false);
  const [systemStatus, setSystemStatus] = useState<SystemStatusType>('error');
  const [loadingMessage, setLoadingMessage] = useState('checking');

  const pingSystem = async () => {
    try {
      setSystemStatusLoading(true);
      setLoadingMessage('checking');
      const response = await fetch('/api/system/ping');
      if (!response.ok) {
        throw new Error('Health check failed: ' + response.statusText);
      }
      const data = await response.json();
      // Extract systemstatus from the response
      const tickerStatus = data.tickerStatus;
      let systemStatusValue: any = 'checking...';
      
      // Check if any ticker has timeSinceLastData > 1000
      if (tickerStatus && tickerStatus.tickCollector) {
        const unhealthyWorkers: number[] = [];
        Object.values(tickerStatus.tickCollector).forEach((worker: any) => {
          if (!worker.isConnected) { // || worker.timeSinceLastData > 1000
            unhealthyWorkers.push(worker.id);
          }
        });
        
        if (unhealthyWorkers.length > 0) {
          systemStatusValue = {
            status: 'unhealthy',
            unhealthyWorkers: unhealthyWorkers
          };
        } else {
          systemStatusValue = {
            status: 'healthy',
            unhealthyWorkers: unhealthyWorkers
          };
        }
      } else {
        systemStatusValue = {
          status: 'idle',
          unhealthyWorkers: []
        };
      }
      // console.log('systemStatusValue', tickerStatus, systemStatusValue, data)
      setSystemStatus(systemStatusValue.status === 'healthy' ? 'healthy' : 'unhealthy');
    } catch (error: unknown) {
      setSystemStatus('unhealthy');
      console.log('System health check error:', (error as Error).message);
    } finally {
      setSystemStatusLoading(false);
    }
  };

  // const pingSystem_passive = pingSystem;
  const pingSystem_passive = async () => {
    
    try {
      setLoadingMessage('checking');
      const response = await fetch('/api/system/ping');
      if (!response.ok) {
        throw new Error('Health check failed: ' + response.statusText);
      }
      const data = await response.json();
      // Extract systemstatus from the response
      const tickerStatus = data.tickerStatus;
      let systemStatusValue: any = 'checking...';
      
      // Check if any ticker has timeSinceLastData > 1000
      if (tickerStatus && tickerStatus.tickCollector) {
        const unhealthyWorkers: number[] = [];
        Object.values(tickerStatus.tickCollector).forEach((worker: any) => {
          if (worker.timeSinceLastData > 1000) {
            unhealthyWorkers.push(worker.id);
          }
        });
        
        if (unhealthyWorkers.length > 0) {
          systemStatusValue = {
            status: 'unhealthy',
            unhealthyWorkers: unhealthyWorkers
          };
        } else {
          systemStatusValue = {
            status: 'healthy',
            unhealthyWorkers: unhealthyWorkers
          };
        }
      } else {
        systemStatusValue = {
          status: 'idle',
          unhealthyWorkers: []
        };
      }
      // console.log('systemStatusValue', tickerStatus, systemStatusValue, data)
      setSystemStatus(systemStatusValue.status === 'healthy' ? 'healthy' : 'unhealthy');
    } catch (error: unknown) {
      setSystemStatus('unhealthy');
      console.log('System health check error:', (error as Error).message);
    } finally {
    }
  };

  const startStopSystem = async (type: 'start' | 'stop') => {
    try {
      setSystemStatusLoading(true);
      setLoadingMessage(type === 'start' ? 'starting' : 'stopping');
      const endpoint = type === 'start' ? 'http://localhost:3000/server/start' : 'http://localhost:3000/server/stop';

      console.log(`${type} operation started...`);
      const response = await fetch(endpoint, { method: 'POST' });
      if (!response.ok) {
        throw new Error(`${type} failed with status: ${response.status}`);
      }

      if (type === 'start') {
        // setLoadingMessage('waiting for system');
        console.log('Waiting for system to fully start...', Date.now());
        await new Promise(resolve => setTimeout(resolve, 3000));
      }
      // await pingSystem();

      console.log('Pinging system...', Date.now());
      await pingSystem()
      console.log('Pinged system...', Date.now());
    } catch (error: unknown) {
      console.error(`${type} error:`, error);
      setSystemStatusLoading(false);
    }
  };

  useEffect(() => {
    pingSystem();
    const interval = setInterval(pingSystem_passive, 10000); // Refresh every 60 seconds
    return () => clearInterval(interval);
  }, []);

  return (
    <SystemStatusContext.Provider value={{ systemStatus, systemStatusLoading, loadingMessage, pingSystem, startStopSystem }}>
      {children}
    </SystemStatusContext.Provider>
  );
};

export const useSystemHealthStatus = () => {
  const context = useContext(SystemStatusContext);
  if (context === undefined) {
    throw new Error('useSystemHealthStatus must be used within a SystemStatusProvider');
  }
  return context;
};
