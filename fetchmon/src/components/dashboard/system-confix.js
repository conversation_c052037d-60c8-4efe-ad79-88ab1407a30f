/* eslint-disable @typescript-eslint/no-unused-expressions */
import { useState, useEffect, useMemo, useCallback, memo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { useServerStatus } from '@/context/ServerStatusContext';
import { Settings, Edit, ChevronDown, ChevronUp } from 'lucide-react';

export default function SystemConfig(props) {
  const { serverStatus, serverStatusLoading, loadingMessage, pingServer, startStopServer } = useServerStatus();


  const [serverStatusText, setServerStatusText] = useState(serverStatus); 
  // const [battleParamsBase, setbattleParamsBase] = useState({});
  const [battleParams, setbattleParamsFN] = useState({});
  const [serverConfig, setServerConfig] = useState(null);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [editStarted, seteditStarted] = useState(false);

  useEffect(() => {
    const fetchServerConfig = async () => {
      try {
        const response = await fetch('/api/server/config');
        const data = await response.json();
        setServerConfig(data);
        // Also set the config part to battleParams
        // setbattleParamsBase(data.config || {});
        setbattleParamsFN(data.config || {});
      } catch (error) {
        console.error('Failed to fetch server config:', error);
      }
    };
    fetchServerConfig();
  }, []);

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  function jsonAyniMi(veriA, veriB, karsilastirilacakObjeAnahtarlari = ["websocket", "intervals", "pairs"]) {
    for (let anahtar of karsilastirilacakObjeAnahtarlari) {
      const degerA = veriA[anahtar];
      const degerB = veriB[anahtar];

      if (typeof degerA !== typeof degerB) {
        return false;
      }

      if (degerA === null || degerB === null) {
        if (degerA !== degerB) return false;
      } else if (typeof degerA === 'object') {
        if (JSON.stringify(degerA) !== JSON.stringify(degerB)) return false;
      } else {
        if (degerA !== degerB) return false;
      }
    }

    return true;
  }

  const setParameters = val => {
    if (serverStatus !== 'healthy') {
      let currV = JSON.parse(JSON.stringify(battleParams));
      let nVal = {
        ...currV,
      };
      let { fname, fvar } = val;
      nVal[fname] = fvar;
      seteditStarted(!jsonAyniMi(nVal, serverConfig?.config));
      // console.log('set battleParams', battleParams);
      // console.log('set nVal', nVal);
      // console.log('set serverConfig', serverConfig);
      // console.log('jsonAyniMi', jsonAyniMi(nVal, serverConfig?.config));

      setbattleParamsFN(nVal);
    }
  }

  const handleEdit = () => {
    // Logic to handle edit action, e.g., open a modal or redirect to config page
    console.log('Edit configuration clicked');
    isCollapsed && serverStatus !== 'healthy' ? setIsCollapsed(false) : null;
  };

  const generateKey = ({ inclTime = false }) => {
    let randStr = (+new Date * Math.random()).toString(36).substring(0, 6);
    let resp = inclTime ? Date.now().toString() + '-' + randStr : randStr;
    return resp
  };

  const handleSave = async () => {  
    if (serverStatus === 'healthy') {
      alert('Server is running, cannot save configuration');
      return;
    } 
    try {
      let configID = generateKey({ inclTime: false });
      setParameters({ fname: 'configID', fvar: configID });
      let d2Save = battleParams;
      d2Save.configID = configID;
      console.log('Saving configuration:', d2Save);
      const response = await fetch('/api/server/config/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(d2Save),
      });
      if (!response.ok) {
        throw new Error('Failed to save configuration');
      }
      const data = await response.json();
      console.log('Configuration saved:', data);
      seteditStarted(false);
    } catch (error) {
      console.error('Error saving configuration:', error);
    }
  };


  useEffect(() => {
    // console.log('serverStatus useEffect', serverStatus);
    if (serverStatus === 'healthy' && serverStatusText !== serverStatus) {
      // console.log('Server is started, collapse config panel');
      setIsCollapsed(true)
    }
      setServerStatusText(serverStatus);
  }, [serverStatus]);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            System Configuration <span className="text-xs text-muted-foreground ml-2">{battleParams?.configID ? `${battleParams.configID}` : ''}</span>
            {/* <span>{serverStatusText}</span> */}
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={toggleCollapse}>
              {isCollapsed ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />}
            </Button>
            <Button variant="outline" size="sm" onClick={editStarted ? handleSave : handleEdit} className={editStarted ? 'bg-primary text-primary-foreground hover:bg-primary/20' : null}>
              <Edit className="h-4 w-4 mr-2" />
              {serverStatus !== 'healthy' ? (editStarted ? 'Save' : 'Edit') : 'View'}
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      {!isCollapsed && (
        <CardContent>
          <div className="flex items-start justify-start mb-2">
            <div className="border-b min-h-[37px] min-w-[150px] w-[150px] pb-1">
              <span className="text-sm font-medium block">Kline Interval</span>
            </div>

            <div className="border-b border-divider min-h-[35px] ml-2 w-full pb-1">
              <Intervals {...props} serverStatus={serverStatus} multix={true} battleParams={battleParams} candleCounts={true} callbackFN={setParameters} />
            </div>
          </div>

          <div className="flex items-start justify-start mb-2">
            <div className="border-b min-h-[37px] min-w-[150px] w-[150px] pb-1">
              <span className="text-sm font-medium block">Symbols</span>
            </div>

            <div className="border-b border-divider min-h-[35px] ml-2 w-full pb-1">
              <Pairs {...props} battleParams={battleParams} callbackFN={setParameters} />
            </div>
          </div>

          <div className="flex items-start justify-start mb-2">
            <div className="border-b min-h-[37px] min-w-[150px] w-[150px] pb-1">
              <span className="text-sm font-medium block">WebSocket Config</span>
            </div>

            <div className="border-b border-divider min-h-[35px] ml-2 w-full pb-1">
              <WebSocks {...props} battleParams={battleParams} callbackFN={setParameters} />
            </div>
          </div>
{/* 
          <div className="flex justify-between txt-sm mt-4">
            <span className="text-muted-foreground">parameters:</span>
            <span>{JSON.stringify(battleParams, null, 2)}</span>
          </div> */}

        </CardContent>
      )}
    </Card>
  );
}


const WebSocks = props => { 
  const [wsParams, setWsParams] = useState({});

  useEffect(() => {
    if (props.battleParams && props.battleParams.websocket) {
      setWsParams(props.battleParams.websocket);
    }
  }, [props.battleParams]); 

  // const handleParamChange = (param, value) => {
  //   setWsParams(prev => ({ ...prev, [param]: value }));
  //   props.callbackFN && props.callbackFN({ fname: 'websocket', fvar: { ...wsParams, [param]: value } });
  // };

  const handleParamChange = (paramName, value) => {
    let curW = wsParams;
    curW[paramName] = value;
    setWsParams(curW);
    props.callbackFN && props.callbackFN({ fname: 'websocket', fvar: curW });
  }

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
        <div className="flex align-center">
          <span className="text-muted-foreground pt-1 w-44">Pairs per Connection:</span>
          <span className="ml-2">
            <Input
              id="pairsPerConnection"
              type="number"
              placeholder="0"
              value={wsParams.pairsPerConnection || ''}
              onChange={(e) => handleParamChange('pairsPerConnection', e.target.value)}
              className="h-7 w-16 text-xs px-2"
            />
          </span>
        </div>
        <div className="flex align-center">
          <span className="text-muted-foreground pt-1 w-44">Health Check Interval:</span>
          <span className="ml-2 text-muted-foreground">
            <Input
              id="healthCheckInterval"
              type="number"
              placeholder="0"
              value={wsParams.healthCheckInterval || ''}
              onChange={(e) => handleParamChange('healthCheckInterval', e.target.value)}
              className="h-7 w-24 text-xs px-2"
            />
          </span>
        </div>
 
        <div className="flex align-center">
          <span className="text-muted-foreground pt-1 w-44">Reconnect Interval:</span>
          <span className="ml-2 text-muted-foreground">
            <Input
              id="reconnectInterval"
              type="number"
              placeholder="0"
              value={wsParams.reconnectInterval || ''}
              onChange={(e) => handleParamChange('reconnectInterval', e.target.value)}
              className="h-7 w-24 text-xs px-2"
            />
          </span>
        </div>


        <div className="flex align-center">
          <span className="text-muted-foreground pt-1 w-44">Max Reconnect Attempts:</span>
          <span className="ml-2 text-muted-foreground">
            <Input
              id="maxReconnectAttempts"
              type="number"
              placeholder="0"
              value={wsParams.maxReconnectAttempts || ''}
              onChange={(e) => handleParamChange('maxReconnectAttempts', e.target.value)}
              className="h-7 w-24 text-xs px-2"
            />
          </span>
        </div>
         
      </div>
      </div>
  );
};


const Intervals = props => {
  const tagsData = ["1m", "5m", "15m", "30m", "1h", "2h", "4h", "6h", "12h", "1d"];
  const [selectedTags, setselectedTags] = useState([]);
  const [candles, setCandles] = useState(100);

  const handleChange = (tag, checked) => {
    if (props.serverStatus === 'healthy') return;
    if (props.multi) {
      const nextSelectedTags = checked ? [...selectedTags, tag] : selectedTags.filter(t => t !== tag);
      const newA = [...new Set(nextSelectedTags)];
      setselectedTags(newA);
      props?.callbackFN && props?.callbackFN({ fname: 'intervals', fvar: newA });
    } else {
      const nextSelectedTags = checked ? [tag] : selectedTags.filter(t => t !== tag);
      setselectedTags(nextSelectedTags);
      props?.callbackFN && props?.callbackFN({ fname: 'intervals', fvar: nextSelectedTags });
    }
  };

  const handleCandle = (vals) => {
    let nVal = vals?.target?.value ? parseFloat(vals?.target.value) : vals;
    setCandles(nVal);
    props.candleCounts && props?.callbackFN && props.callbackFN({ fname: 'candleCounts', fvar: nVal });
  };

  useEffect(() => {
    // if (props.battleParams && (props.battleParams.intervals || props.battleParams.battleInterval || props.battleParams.candleCounts)) {
    //   let parami = props.multi ? props.battleParams.intervals : props.battleParams.battleInterval;
    //   let bars = props.battleParams && props.battleParams.candleCounts;
    //   bars && candles !== bars && setCandles(bars);
    //   parami && JSON.stringify(parami) !== JSON.stringify(selectedTags) && setselectedTags(parami);
    // }
    if (props.battleParams && (props.battleParams.intervals || props.battleParams.battleInterval || props.battleParams.candleCounts)) {
      let parami = !props.multi ? props.battleParams.intervals : props.battleParams.battleInterval;
      let bars = props.battleParams && props.battleParams.candleCounts;
      bars && candles !== bars && setCandles(bars);
      parami && JSON.stringify(parami) !== JSON.stringify(selectedTags) && setselectedTags(parami);
    }
  }, [props.battleParams]);

  return (
    <div className="flex flex-row items-start justify-start mr-4">
      {tagsData.map(tag => (
        <button
          key={tag}
          onClick={() => handleChange(tag, !(Array.isArray(selectedTags) && selectedTags.includes(tag)))}
          className={`px-3 py-1 text-sm rounded-md border mr-1 transition-colors ${Array.isArray(selectedTags) && selectedTags.includes(tag)
              ? 'bg-primary text-primary-foreground border-primary'
              : 'bg-background text-foreground border-border hover:bg-accent'
            }`}
        >
          {tag}
        </button>
      ))}

    </div>
  );
};

// export default CardParamsTimes;


const fetcher = (url) => fetch(url).then((r) => r.json()).catch(e => console.log('fetcher error', url, e));

function BinanceFuturesDaily({ pair, forceupdate = false } = {}) {
  return new Promise(async (resolve) => {
    try {
      var uri = '/api/server/dims/symbols?detailed=true&schema=pairlist'
      uri += pair ? '&pair=' + pair : ''
      uri += forceupdate ? '&forceupdate=true' : '';
      console.log(uri)
      const data = await fetcher(uri)
      resolve(data)
    } catch (e) {
      console.log('fetch err', e)
    }
  });
}

function useStickyState(defaultValue, key) {
  const [value, setValue] = useState(defaultValue);
  // console.log('useStickyState', defaultValue, key)
  useEffect(() => {
    const stickyValue = window.localStorage.getItem(key);
    if (stickyValue && stickyValue !== 'null') {
      // console.log('stickyValue2', JSON.parse(stickyValue))
      try { setValue(JSON.parse(stickyValue)); } catch (e) {
        console.log('battle pairs error stickyValue', stickyValue, e)
      }
    } else {
      const fData = async () => {
        // console.log('fData call binance')
        var dt = await BinanceFuturesDaily();
        // console.log('fData JSON.stringify(data)',  JSON.stringify(dt.data))
        window.localStorage.setItem(key, JSON.stringify(dt));
        setValue(JSON.parse(JSON.stringify(dt)));
      }
      fData();
    }
  }, [key]);

  useEffect(() => {
    window.localStorage.setItem(key, JSON.stringify(value));
  }, [key, value]);

  return [value, setValue];
}

// Memoized pair button component for better performance
const PairButton = memo(({ tag, isSelected, onToggle }) => {
  return (
    <button
      onClick={() => onToggle(tag.s, !isSelected)}
      className={`px-2 py-1 text-xs rounded-sm border transition-colors ${isSelected
          ? 'bg-primary text-primary-foreground border-primary'
          : 'bg-background text-foreground border-border hover:bg-accent'
        }`}
      title={`${tag.s} Hacim: $${tag.v}`} // Use native title instead of Tooltip for performance
    >
      {tag.s}
    </button>
  );
});

PairButton.displayName = 'PairButton';

const Pairs = props => {
  const { user = {} } = props;
  const { login } = user;
  const [futuresDaily, setFuturesDaily] = useState(false);
  const [userFavorites, setUserFavorites] = useState(false);
  const [selectedPairs, setselectedPairs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [isOpened, setisOpened] = useState(true);
  const [lastpair, setlastpair] = useState(false);
  const [mode, setMode] = useStickyState(null, 'bPairs');
  const [minVolume, setMinVolume] = useState('');
  const [currentPage, setCurrentPage] = useState(0);
  const [itemsPerPage] = useState(50); // Limit items per page for performance
  const [showAll, setShowAll] = useState(false); // Toggle to show all pairs without pagination

  const handleChange = useCallback((tag, checked) => {
    // Always set the last pair to the current tag being interacted with
    setlastpair(tag);

    let nextSelectedTags;
    if (checked) {
      // Check if tag already exists to avoid duplicates
      if (!selectedPairs.includes(tag)) {
        nextSelectedTags = [...selectedPairs, tag];
      } else {
        return; // No change needed, exit early
      }
    } else {
      // Use findIndex for better performance than filter for single item removal
      const index = selectedPairs.indexOf(tag);
      if (index > -1) {
        nextSelectedTags = [
          ...selectedPairs.slice(0, index),
          ...selectedPairs.slice(index + 1)
        ];
      } else {
        return; // No change needed, exit early
      }
    }

    // Update state
    setselectedPairs(nextSelectedTags);

    // Only execute callback if mode is truthy
    if (mode) {
      props.callbackFN({ fname: 'pairs', fvar: nextSelectedTags });
      // Update lastpair to the last item in the array if adding, or keep current tag if removing
      if (nextSelectedTags.length > 0) {
        setlastpair(checked ? nextSelectedTags[nextSelectedTags.length - 1] : tag);
      }
    }
  }, [selectedPairs, mode, props]);

  useEffect(() => {
    if (!mode) {
      setLoading(true);
    } else {
      setFuturesDaily(mode?.data);
      setLoading(false);
    }
  }, [mode]);

  useEffect(() => {
    // console.log('Pairs useEffect', props.battleParams, selectedPairs);
    if (props.battleParams) {
      let parami = props.battleParams.pairs;
      // Use a more efficient comparison to avoid unnecessary JSON.stringify calls
      if (parami && (!selectedPairs || parami.length !== selectedPairs.length ||
        !parami.every((pair, index) => pair === selectedPairs[index]))) {
        setselectedPairs(parami);
      }
    } else {
      console.log('no default for pairs');
    }
  }, [props.battleParams, selectedPairs]);

  // Memoize the pairs array to avoid recalculating on every render
  const allPairs = useMemo(() => {
    return Array.isArray(futuresDaily) ? futuresDaily.map(p => p.s) : [];
  }, [futuresDaily]);

  // Memoize the selected pairs count for performance
  const selectedCount = useMemo(() => {
    return Array.isArray(selectedPairs) ? selectedPairs.length : 0;
  }, [selectedPairs]);

  // Memoize paginated pairs for performance
  const paginatedPairs = useMemo(() => {
    if (!Array.isArray(futuresDaily)) return [];
    if (showAll) return futuresDaily; // Show all pairs if toggle is enabled
    const startIndex = currentPage * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return futuresDaily.slice(startIndex, endIndex);
  }, [futuresDaily, currentPage, itemsPerPage, showAll]);

  // Memoize total pages
  const totalPages = useMemo(() => {
    if (!Array.isArray(futuresDaily)) return 0;
    return Math.ceil(futuresDaily.length / itemsPerPage);
  }, [futuresDaily, itemsPerPage]);

  const refresh = async () => {
    setLoading(true);
    try {
      setFuturesDaily([]);
      var dt = await BinanceFuturesDaily({ forceupdate: true });
      setFuturesDaily(dt?.data);
      setMode(dt);
      setLoading(false);
    } catch (error) {
      console.error('Error refreshing pairs data:', error);
      setLoading(false);
    }
  };

  const selectA = useCallback(() => {
    // Early return if no data
    if (allPairs.length === 0) {
      setselectedPairs([]);
      mode && props.callbackFN({ fname: 'pairs', fvar: [] });
      mode && setlastpair(null);
      return;
    }

    // Reset to first page when selecting all
    setCurrentPage(0);

    // Use memoized pairs array
    setselectedPairs(allPairs);

    // Only execute callback and lastpair logic if mode is truthy
    if (mode) {
      props.callbackFN({ fname: 'pairs', fvar: allPairs });
      // Optimize last pair selection - avoid slice and pop
      if (allPairs.length > 0) {
        setlastpair(allPairs[allPairs.length - 1]);
      }
    }
  }, [allPairs, mode, props]);

  const setRandomPairs = useCallback((n) => {
    // Early return if no data
    if (allPairs.length === 0) {
      setselectedPairs([]);
      mode && props.callbackFN({ fname: 'pairs', fvar: [] });
      mode && setlastpair(null);
      return;
    }

    // Use Fisher-Yates shuffle for better performance than sort()
    const shuffled = [...allPairs];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }

    const selected = shuffled.slice(0, n);
    setselectedPairs(selected);

    // Only execute callback and lastpair logic if mode is truthy
    if (mode) {
      props.callbackFN({ fname: 'pairs', fvar: selected });
      // Optimize last pair selection - avoid slice and pop
      if (selected.length > 0) {
        setlastpair(selected[selected.length - 1]);
      }
    }
  }, [allPairs, mode, props]);

  const fnSelectMinVolume = (vals) => {
    setMinVolume(vals?.target.value ? parseFloat(vals?.target.value) : '');
  };

  const fnSetMinVolumeFilter = () => {
    // Early return if no data or invalid minVolume
    if (!Array.isArray(futuresDaily) || futuresDaily.length === 0 || !minVolume) {
      setselectedPairs([]);
      mode && props.callbackFN({ fname: 'pairs', fvar: [] });
      mode && setlastpair(null);
      return;
    }

    // Pre-calculate threshold once
    const volumeThreshold = parseFloat(minVolume) * 1000000;

    // Single pass filter and map operation
    const array = futuresDaily
      .filter(p => p.vn > volumeThreshold)
      .map(p => p.s);

    setselectedPairs(array);

    // Only execute callback and lastpair logic if mode is truthy
    if (mode) {
      props.callbackFN({ fname: 'pairs', fvar: array });
      // Optimize last pair selection - avoid slice and pop
      if (array.length > 0) {
        setlastpair(array[array.length - 1]);
      }
    }
  };

  const openlink = () => {
    var urlX = 'https://www.tradingview.com/chart?symbol=BINANCE%3A' + lastpair + 'PERP';
    console.log('', new Date().toISOString(), lastpair);
    lastpair && window.open(urlX, "_blank");
  };

  const Favoriler = []; // Bu değişkenin nereden geldiğini belirtmediniz, gerekliyse tanımlayın

  return (
    <div className="w-full">
      <div className="flex flex-row items-start justify-start pb-1 mr-4 flex-wrap gap-1 w-full">
        <Button
          size="sm"
          variant="default"
          className="h-7 px-2 text-xs rounded-md bg-blue-500 hover:bg-blue-600 text-white"
          onClick={() => refresh()}
        >
          Tazele
        </Button>

        {loading && <Skeleton className="h-7 w-7 rounded-full" />}

        {Array.isArray(futuresDaily) && futuresDaily.length !== 0 && (
          <>
            <Button
              size="sm"
              variant="default"
              className="h-7 px-2 text-xs rounded-md bg-blue-500 hover:bg-blue-600 text-white"
              onClick={() => setisOpened(!isOpened)}
            >
              {loading ? 'yükleniyor..' : isOpened ? 'Gizle' : 'Göster'}
            </Button>

            <Button
              size="sm"
              variant="default"
              className="h-7 px-2 text-xs rounded-md bg-cyan-400 hover:bg-cyan-500 text-black"
              onClick={() => {
                setselectedPairs(Favoriler);
                mode && props.callbackFN({ fname: 'pairs', fvar: Favoriler });
                mode && Array.isArray(Favoriler) && setlastpair(Favoriler.slice(-1).pop());
              }}
            >
              Favoriler
            </Button>

            <Button
              size="sm"
              variant="default"
              className="h-7 px-2 text-xs rounded-md bg-cyan-400 hover:bg-cyan-500 text-black"
              onClick={() => {
                let px = ["ADAUSDT", "BNBUSDT", "SOLUSDT", "ETHUSDT", "FILUSDT", "XRPUSDT", "DYDXUSDT"];
                setselectedPairs(px);
                mode && props.callbackFN({ fname: 'pairs', fvar: px });
                mode && Array.isArray(px) && setlastpair(px.slice(-1).pop());
              }}
            >
              Rx
            </Button>

            <Button
              size="sm"
              variant="default"
              className="h-7 px-2 text-xs rounded-md bg-cyan-400 hover:bg-cyan-500 text-black"
              onClick={() => setRandomPairs(2)}
            >
              R2
            </Button>

            <Button
              size="sm"
              variant="default"
              className="h-7 px-2 text-xs rounded-md bg-cyan-400 hover:bg-cyan-500 text-black"
              onClick={() => setRandomPairs(5)}
            >
              R5
            </Button>

            <Button
              size="sm"
              variant="default"
              className="h-7 px-2 text-xs rounded-md bg-cyan-400 hover:bg-cyan-500 text-black"
              onClick={() => setRandomPairs(15)}
            >
              R15
            </Button>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center gap-1">
                    <Input
                      id="minVolume"
                      type="number"
                      placeholder="0"
                      value={minVolume}
                      onChange={fnSelectMinVolume}
                      className="h-7 w-16 text-xs px-2"
                    />
                    <Button
                      size="sm"
                      variant="default"
                      className="h-7 px-2 text-xs rounded-md bg-cyan-400 hover:bg-cyan-500 text-black h-7"
                      onClick={fnSetMinVolumeFilter}
                    >
                      set
                    </Button>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Set Minimum Volume For Selection (xMillion)</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <Button
              size="sm"
              variant="default"
              className="h-7 px-2 text-xs rounded-md bg-cyan-400 hover:bg-cyan-500 text-black"
              onClick={selectA}
            >
              Hepsi
            </Button>

            <Button
              size="sm"
              variant="default"
              className="h-7 px-2 text-xs rounded-md bg-cyan-400 hover:bg-cyan-500 text-black"
              onClick={() => {
                mode && props.callbackFN({ fname: 'pairs', fvar: [] });
                mode && setlastpair(null);
                setselectedPairs([]);
              }}
            >
              Hiçbirisi
            </Button>

            <Badge variant="default" className="h-7 px-2 text-xs rounded-md bg-cyan-400 text-black">
              Selected: #{selectedCount}
            </Badge>

            <Button
              size="sm"
              variant="default"
              className="h-7 px-2 text-xs rounded-md bg-orange-500 hover:bg-orange-600 text-black"
              onClick={openlink}
            >
              TV
            </Button>
          </>
        )}
      </div>

      {isOpened && (
        <div className="w-full mt-2">
          {/* Pagination controls */}
          {Array.isArray(futuresDaily) && futuresDaily.length > itemsPerPage && (
            <div className="flex items-center justify-between mb-2 text-xs">
              <span className="text-muted-foreground">
                {showAll ? `Showing all ${futuresDaily.length} pairs` : `Page ${currentPage + 1} of ${totalPages} (${futuresDaily.length} total pairs)`}
              </span>
              <div className="flex gap-1">
                <Button
                  size="sm"
                  variant={showAll ? "default" : "outline"}
                  className="h-6 px-2 text-xs"
                  onClick={() => setShowAll(!showAll)}
                >
                  {showAll ? "Paginate" : "Show All"}
                </Button>
                {!showAll && (
                  <>
                    <Button
                      size="sm"
                      variant="outline"
                      className="h-6 px-2 text-xs"
                      onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}
                      disabled={currentPage === 0}
                    >
                      ←
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      className="h-6 px-2 text-xs"
                      onClick={() => setCurrentPage(Math.min(totalPages - 1, currentPage + 1))}
                      disabled={currentPage === totalPages - 1}
                    >
                      →
                    </Button>
                  </>
                )}
              </div>
            </div>
          )}

          {/* Optimized pairs list with pagination */}
          <div className="overflow-auto max-h-[300px]">
            <div className="flex flex-wrap gap-1">
              {paginatedPairs.map(tag => {
                const isSelected = selectedPairs.includes(tag.s);
                return (
                  <PairButton
                    key={tag.s}
                    tag={tag}
                    isSelected={isSelected}
                    onToggle={handleChange}
                  />
                );
              })}
              {paginatedPairs.length === 0 && (
                <span className="text-xs text-muted-foreground">
                  No pairs to display.
                </span>
              )}
            </div>
          </div>

          {/* Summary info */}
          <div className="mt-2 text-xs text-muted-foreground">
            Showing {paginatedPairs.length} of {futuresDaily.length} pairs
          </div>
        </div>
      )}
    </div>
  );
};


// Pairs
