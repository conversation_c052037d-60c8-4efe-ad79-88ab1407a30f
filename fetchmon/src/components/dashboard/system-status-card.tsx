import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertTriangle, CheckCircle } from 'lucide-react';
import { useSystemHealthStatus } from '@/context/SystemStatusContext';
import { useServerStatus } from '@/context/ServerStatusContext';

export function SystemStatusCard() {
  const { 
    systemStatus: systemHealthStatus, 
    systemStatusLoading: systemHealthLoading, 
    loadingMessage: systemLoadingMessage, 
    pingSystem, 
    startStopSystem 
  } = useSystemHealthStatus();

  const { serverStatus } = useServerStatus();
  
  const getstatus = () => {
    pingSystem();
  }

  const bgColor = systemHealthStatus === 'healthy' ? 'bg-yellow-200' : '';

  return (
    <Card className={`${bgColor} w-70 py-4 gap-2 min-h-[120px]`}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">
          System Status &nbsp;&nbsp;
          <span className='text-xs text-muted-foreground cursor-pointer' onClick={getstatus}>
            check
          </span>
        </CardTitle>
        {systemHealthStatus === 'healthy' ? (
          <CheckCircle className="h-4 w-4 text-green-500" />
        ) : (
          <AlertTriangle className="h-4 w-4 text-red-500" />
        )}
      </CardHeader>
      <CardContent className='flex justify-between'>
        <div>
          <div className="text-2xl font-bold">
            {systemHealthLoading ? (
              <div className="h-6 w-20 bg-gray-200 rounded animate-pulse" />
            ) : systemHealthStatus === 'healthy' ? (
              'WIP'
            ) : (
              (serverStatus == 'healthy' ?  'Ready' : 'Passive')
            )}
          </div>
          <div className="text-xs text-muted-foreground">
            {systemHealthLoading ? (
              <div className="h-3 w-24 bg-gray-200 rounded animate-pulse mt-1" />
            ) : systemHealthStatus === 'healthy' ? (
              'Fetching ticks'
            ) : (
              (serverStatus == 'healthy' ?  'System is not active' : 'Wait for server to start')
            )}
          </div>
        </div>
        <div className='flex justify-end'>
          <span className='text-xs text-muted-foreground text-align-right'>
            {systemHealthLoading ? (
              systemLoadingMessage
            ) : systemHealthStatus === 'healthy' ? (
              <Button
                variant="outline"
                disabled={serverStatus !== 'healthy'}
                size="sm"
                onClick={async () => {
                  if (window.confirm('Are you sure you want to stop the system?')) {
                    await startStopSystem('stop');
                  }
                }}
              >
                Stop
              </Button>
            ) : (
              <Button
                className={bgColor}
                variant="outline"
                disabled={serverStatus !== 'healthy'}
                size="sm"
                onClick={async () => {
                  if (window.confirm('Are you sure you want to start the system?')) {
                    await startStopSystem('start');
                  }
                }}
              >
                Start
              </Button>
            )}
          </span>
        </div>
      </CardContent>
    </Card>
  );
}
