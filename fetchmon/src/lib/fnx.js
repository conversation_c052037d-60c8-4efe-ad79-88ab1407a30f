/* eslint-disable @typescript-eslint/no-require-imports */
const path = require('path');
const Database = require('better-sqlite3');
const fs = require('fs/promises');

const timestamp = () => `[${new Date().toUTCString()}]`;
const logg = exports.logg = (...args) => console.log(timestamp(), ...args)

const dbPath = path.join(process.cwd(), 'data/fetchmon.db');
const db = new Database(dbPath);
logg('Connected to the logs database.');

const log2DB = exports.log2DB = (...args) => {
    return new Promise((resolve, reject) => {
        const logTexts = args.map(arg => {
            if (typeof arg === 'object') {
                return JSON.stringify(arg);
            }
            return String(arg);
        }).join(' ');
        const sql = `INSERT INTO logs (logTexts, dtCreated) VALUES (?, DATETIME('now'))`;
        logg(timestamp(), sql);
        try {
            const stmt = db.prepare(sql);
            const result = stmt.run(logTexts);
            resolve(result.lastInsertRowid);
        } catch (err) {
            logg('Error logging to database:', err);
            reject(err);
        }
    });
};

const log2File = exports.log2File = async (message) => {
    const logFilePath = path.join(process.cwd(), 'logs.log');
    try {
        await fs.appendFile(logFilePath, `${new Date().toISOString()} - ${typeof message == 'object' ? JSON.stringify(message) : message}\n`);
    } catch (error) {
        logg('Error writing to log file:', error);
    }
};

const dbFunctions = {
    createLogsTable: async () => {
        try {
            db.exec(`
            CREATE TABLE IF NOT EXISTS logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                logTexts TEXT,
                dtCreated DATETIME
            )
        `);
        } catch (err) {
            logg('Error creating logs table:', err);
            throw err;
        }
    },
    createConfigTable: async () => {
        return new Promise((resolve, reject) => {
            try {
                db.exec(`
                CREATE TABLE IF NOT EXISTS config (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    dtCreated DATETIME,
                    config TEXT
                    )
            `);
                resolve(1);

            } catch (err) {
                logg('Error creating config table:', err);
                resolve(0);
            }
        });
    },

    createDimsTable: async () => {
        return new Promise((resolve, reject) => {
            try {
                db.exec(`
                CREATE TABLE IF NOT EXISTS dims (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    dtCreated DATETIME,
                    dim TEXT,
                    value TEXT
                    )
            `);
                resolve(1);

            } catch (err) {
                logg('Error creating dims table:', err);
                resolve(0);
            }
        });
    },
    addInitialConfig: async () => {
        try {
            // Insert initial configuration if table is empty
            const countStmt = db.prepare('SELECT COUNT(*) as count FROM config');
            const result = countStmt.get();
            if (result.count === 0) {
                const initialConfig = {
                    "configID": 'a001x',
                    "websocket": {
                        "pairsPerConnection": 25,
                        "healthCheckInterval": 10000,
                        "reconnectInterval": 5000,
                        "maxReconnectAttempts": 10
                    },
                    "pairs": [
                        "VETUSDT","PLUMEUSDT","OPUSDT","FLOWUSDT","HOOKUSDT","LOOMUSDT","TAGUSDT","AVAAIUSDT","1000SATSUSDT","ALPACAUSDT","AUSDT","AIXBTUSDT","RDNTUSDT"
                    ],
                    "intervals": ["1m"]
                };
                const insertStmt = db.prepare('INSERT INTO config (dtCreated, config) VALUES (DATETIME(\'now\'), ?)');
                insertStmt.run(JSON.stringify(initialConfig));
                logg('Initial configuration inserted successfully');
            }
        } catch (err) {
            logg('Error adding initial config:', err);
            throw err;
        }
    },
    addInitialDims: async () => {
        try {
            // Insert initial dimensions if table is empty
            const countStmt = db.prepare('SELECT COUNT(*) as count FROM dims');
            const result = countStmt.get();
            if (result.count === 0) {
                const initialDims = [ 
                    { dim: 'klineIntervals', value: JSON.stringify([
                        "1m", "3m", "5m", "15m", "30m", "1h", "2h", "4h",
                        "6h", "8h", "12h", "1d", "3d", "1w", "1M"
                    ]) }
                ];
                const insertStmt = db.prepare('INSERT INTO dims (dtCreated, dim, value) VALUES (DATETIME(\'now\'), ?, ?)');
                for (const dim of initialDims) {
                    insertStmt.run(dim.dim, dim.value);
                }
                logg('Initial dimensions inserted successfully');
            }
        } catch (err) {
            logg('Error adding initial dims:', err);
            throw err;
        }
    },
    initConfigTable: async () => {
        try {
            let cc = await dbFunctions.createConfigTable();
            if (cc == 1) {
                dbFunctions.addInitialConfig();
                logg('initial records added to config tables successfully');
            };

        } catch (err) {
            logg('Error adding initial records to table:', err);
            throw err;
        }
    },

    initDimsTable: async () => {
        try {
            let cc = await dbFunctions.createDimsTable();
            if (cc == 1) {
                dbFunctions.addInitialDims();
                logg('initial records added to dims tables successfully');
            };
        } catch (err) {
            logg('Error adding initial records to table:', err);
            throw err;
        }
    }
};

exports.db = {
    init: async () => {
        await dbFunctions.initConfigTable();
        await dbFunctions.initDimsTable();
    },
    initDims: async () => {
        await dbFunctions.initDimsTable();
    },
}

// Call createLogsTable when the module is loaded
try {
    dbFunctions.createLogsTable();
} catch (err) {
    logg('Failed to create logs table:', err);
} 
