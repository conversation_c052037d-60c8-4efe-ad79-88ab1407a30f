// Types for the Binance Tick Collector API responses

export interface SystemHealth {
  status: string;
  timestamp: string;
  uptime: number;
}

export interface RedisStatus {
  connected: boolean;
  info?: string;
  serverTime?: [number, number];
  error?: string;
}

export interface TickCollectorStatus {
  [connectionId: number]: {
    id: number;
    url: string;
    isConnected: boolean;
    lastDataReceived: number;
    timeSinceLastData: number;
    reconnectAttempts: number;
    maxReconnectAttempts: number;
  };
}

export interface TickWorkerStatus {
  workerCount: number;
  processedCount: number;
  uptime: number;
  bufferSizes: Array<{
    symbol: string;
    size: number;
  }>;
}

export interface SystemStatus {
  timestamp: string;
  system: {
    redis: RedisStatus;
    tickCollector: TickCollectorStatus;
    tickWorker: TickWorkerStatus;
  };
  tickerStatus: {
    tickCollector: TickCollectorStatus;
    tickWorker: TickWorkerStatus;
  };
}

export interface QueueCounts {
  waiting: number;
  active: number;
  completed: number;
  failed: number;
  delayed: number;
}

export interface MemoryUsage {
  heapUsed: number;
  heapTotal: number;
  rss: number;
  external: number;
}

export interface MetricsData {
  tickQueueJobs: {
    waiting: number;
    active: number;
    completed: number;
    failed: number;
    delayed: number;
  };
  processUptime: number;
  processMemory: MemoryUsage;
}

export interface TradingPair {
  symbol: string;
  connID: number;
  status: 'active' | 'warning' | 'error' | 'stopped';
  lastUpdate: number;
  errorCount: number;
  recordCount: number;
  closedKlineCount: number;
}

export interface PairData {
  symbol: string;
  lastRecordTime: number;
  totalRecords: number;
  closedKlineCount: number;
  lastTimestamp: string;
}

export interface TradingPairsApiResponse {
  success: boolean;
  count: number;
  tradingPairs: PairData[];
}

export interface SystemInfo {
  cpuCores: number;
  totalMemory: number;
  platform: string;
  arch: string;
  nodeVersion: string;
}
